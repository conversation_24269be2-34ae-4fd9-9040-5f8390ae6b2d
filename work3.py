# encoding: utf-8
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
import numpy as np

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def task3():
    # 加载数据
    df = pd.read_excel("housing.xlsx")
    # 数据预处理
    if df['totalBedrooms'].isnull().sum() > 0:
        median_bedrooms = df['totalBedrooms'].median()
        df['totalBedrooms'].fillna(median_bedrooms, inplace=True)

    # 选择用于聚类的特征
    features = ['housingMedianAge', 'totalRooms', 'totalBedrooms', 'population', 'households', 'medianIncome', 'medianHouseValue']
    X = df[features]

    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 构建和训练KMeans模型
    kmeans = KMeans(n_clusters=4)
    df['cluster'] = kmeans.fit_predict(X_scaled)

    # 模型效果评估
    silhouette_avg = silhouette_score(X_scaled, df['cluster'])

    # 结果解释
    cluster_analysis = df.groupby('cluster')[features].mean().round(2)
    print(cluster_analysis)

    # 可视化聚类结果
    plt.figure(figsize=(12, 8))
    scatter = plt.scatter(df['medianIncome'], df['medianHouseValue'], c=df['cluster'], cmap='viridis', alpha=0.7)
    plt.title(f'住房群组可视化 (K=4)', fontsize=16)
    plt.xlabel('收入中位数')
    plt.ylabel('房价中位数')
    plt.colorbar(scatter, label='簇编号')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('housing_clusters.png')

if __name__ == '__main__':
    task3()