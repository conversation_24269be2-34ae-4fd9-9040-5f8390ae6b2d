# encoding: utf-8
import pandas as pd
import matplotlib.pyplot as plt

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def task1():
    df = pd.read_excel("housing.xlsx")
    print("成功读取 housing.xlsx 文件。")
    print("\n数据集前10行：")
    print(df.head(10))

    features = ['housingMedianAge', 'totalRooms', 'totalBedrooms', 'population', 'households', 'medianIncome']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    fig.suptitle('房价中位数与其他特征的散点图', fontsize=16)

    flat_axes = axes.flatten() # 转换为一维数组，方便后面遍历

    for i in range(len(features)):
        feature = features[i]
        ax = flat_axes[i]

        ax.scatter(df[feature], df['medianHouseValue'], alpha=0.5)
        ax.set_title(f'房价中位数 vs {feature}')
        ax.set_xlabel(feature)
        ax.set_ylabel('房价中位数')

    plt.savefig('image.png')

    numeric_df = df.drop('AreaId', axis=1)
    
    correlation_matrix = numeric_df.corr()
    
    median_house_value_corr = correlation_matrix['medianHouseValue'].sort_values(ascending=False)
    
    print("相关系数:")
    print(median_house_value_corr)

if __name__ == '__main__':
    task1()
