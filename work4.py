# encoding: utf-8
import pandas as pd
from mlxtend.frequent_patterns import apriori, association_rules

def task4():
    df = pd.read_excel('ecom-order-list.xlsx')

    # 数据预处理
    df.dropna(axis=0, subset=['Description'], inplace=True)
    df['Description'] = df['Description'].str.strip()
    # 将发票号转为字符串
    df['InvoiceNo'] = df['InvoiceNo'].astype('str')
    # 移除退货订单
    df = df[df['Quantity'] > 0]

    # 按发票号分组，将商品作为列，统计每个订单中各商品的购买数量
    basket = (df.groupby(['InvoiceNo', 'Description'])['Quantity']
              .sum().unstack().reset_index().fillna(0)
              .set_index('InvoiceNo'))

    # 转换为布尔值（是否购买）
    def encode_units(x):
        return x >= 1

    basket_sets = basket.map(encode_units)
    # 移除 POSTAGE
    if 'POSTAGE' in basket_sets.columns:
        basket_sets.drop('POSTAGE', inplace=True, axis=1)

    # Apriori算法
    frequent_itemsets = apriori(basket_sets, min_support=0.02, use_colnames=True)
    print(f"成功找到 {len(frequent_itemsets)} 个频繁项集")

    # 生成关联规则
    rules = association_rules(frequent_itemsets, metric="lift", min_threshold=1)
    print(f"已生成 {len(rules)} 条关联规则")

    rules = rules.sort_values(['lift', 'confidence'], ascending=[False, False])
    for i, row in rules.head(10).iterrows():
        antecedent = ', '.join(list(row['antecedents']))
        consequent = ', '.join(list(row['consequents']))
        support = row['support']
        confidence = row['confidence']
        lift = row['lift']

        print(f"\n规则 {i+1}: 购买了【{antecedent}】的顾客，有 {confidence:.1%} 的可能会同时购买【{consequent}】。")
        print(f"提升度 {lift:.2f}")

if __name__ == '__main__':
    task4()