# encoding: utf-8
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
import numpy as np

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def task2():
    df = pd.read_excel("housing.xlsx")

    # 处理缺失值
    if df['totalBedrooms'].isnull().sum() > 0:
        median_bedrooms = df['totalBedrooms'].median()
        df['totalBedrooms'].fillna(median_bedrooms, inplace=True)

    # 特征工程
    df['rooms_per_person'] = df['totalRooms'] / df['population']
    df['bedrooms_per_room'] = df['totalBedrooms'] / df['totalRooms']

    # 特征选择和数据分割
    features = ['housingMedianAge', 'totalRooms', 'totalBedrooms',
                'population', 'households', 'medianIncome', 'rooms_per_person', 'bedrooms_per_room']
    target = 'medianHouseValue'

    X = df[features]
    y = df[target]

    # 将数据分割为训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
    print(f"数据集已分割：{len(X_train)}条训练数据，{len(X_test)}条测试数据。")

    # 构建和训练模型
    model = RandomForestRegressor(n_estimators=100)
    model.fit(X_train, y_train)

    # 模型效果评估
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"MSE: {mse:,.2f}")
    print(f"R2: {r2:.4f}")

    # 结果解释
    importances = model.feature_importances_
    feature_importance = pd.DataFrame({'feature': features, 'importance': importances})
    feature_importance = feature_importance.sort_values(by='importance', ascending=False)

    print("各特征对房价预测的重要性排序")
    print(feature_importance)

    # 可视化特征重要性
    plt.figure(figsize=(12, 7))
    plt.barh(feature_importance['feature'], feature_importance['importance'])
    plt.xlabel('重要性')
    plt.ylabel('特征')
    plt.title('特征重要性分析')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig('feature_importance.png')

if __name__ == '__main__':
    task2()
